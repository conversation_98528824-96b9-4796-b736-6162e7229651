# 取餐码功能实现文档

## 📋 需求概述

为到店自取订单添加查看取餐码功能，用户可以通过点击"查看取餐码"按钮获取并查看取餐码信息，方便到店取餐。

## 🎯 具体需求

1. **按钮显示**：在订单列表页面，为自取订单（`delivery_type = 2`）添加"查看取餐码"按钮
2. **API调用**：点击按钮后调用 `GET /v1/user/takeout/order/pickup-code/{orderID}` 接口
3. **信息展示**：在弹窗中显示取餐码和相关订单信息
4. **用户体验**：提供清晰的取餐码显示和使用提示

## 🔧 实现方案

### 1. API接口实现

#### 1.1 API函数定义 (`src/api/takeout.ts`)

```typescript
/**
 * 获取取餐码
 */
export const getPickupCode = async (orderId: number): Promise<PickupCodeInfo> => {
  const response = await http.get<PickupCodeInfo>(
    `/api/v1/user/takeout/order/pickup-code/${orderId}`,
  )
  return response.data
}
```

#### 1.2 类型定义 (`src/api/takeout.typings.ts`)

```typescript
// 取餐码信息
export interface PickupCodeInfo {
  orderID: number
  orderNo: string
  pickupCode: string
  pickupCodeUsed: boolean
  deliveryType: number
  deliveryTypeText: string
  merchantName: string
  merchantAddress: string
  merchantPhone: string
  expectedPickupTime: string
}
```

### 2. 订单列表页面实现

#### 2.1 按钮条件显示

```vue
<!-- 已发货/待收货 (30) -->
<template v-else-if="(order as any).status === 30">
  <!-- 自取订单显示查看取餐码按钮 -->
  <template v-if="(order as any).delivery_type === 2">
    <wd-button type="default" size="small" @click.stop="viewPickupCode(order.id)">
      查看取餐码
    </wd-button>
    <wd-button type="primary" size="small" @click.stop="confirmReceive(order.id)">
      确认收货
    </wd-button>
  </template>
  <!-- 配送订单显示查看物流按钮 -->
  <template v-else>
    <wd-button type="default" size="small" @click.stop="viewLogistics(order.id)">
      查看物流
    </wd-button>
    <wd-button type="primary" size="small" @click.stop="confirmReceive(order.id)">
      确认收货
    </wd-button>
  </template>
</template>
```

#### 2.2 取餐码弹窗

```vue
<!-- 取餐码弹窗 -->
<wd-popup v-model="showPickupCodeModal" position="center" :safe-area-inset-bottom="true">
  <view class="pickup-code-modal">
    <view class="pickup-code-header">
      <text class="pickup-code-title">取餐码</text>
      <wd-icon name="close" size="20" @click="closePickupCodeModal" />
    </view>

    <!-- 加载状态 -->
    <view v-if="pickupCodeLoading" class="pickup-code-loading">
      <wd-loading size="32" />
      <text class="loading-text">获取取餐码中...</text>
    </view>

    <!-- 取餐码内容 -->
    <view v-else-if="pickupCodeInfo" class="pickup-code-content">
      <!-- 取餐码显示区域 -->
      <view class="pickup-code-display">
        <text class="pickup-code-label">取餐码</text>
        <text class="pickup-code-value">{{ pickupCodeInfo.pickupCode }}</text>
        <view class="pickup-code-status" :class="{ used: pickupCodeInfo.pickupCodeUsed }">
          {{ pickupCodeInfo.pickupCodeUsed ? '已使用' : '未使用' }}
        </view>
      </view>

      <!-- 订单信息 -->
      <view class="pickup-order-info">
        <view class="info-row">
          <text class="info-label">订单号：</text>
          <text class="info-value">{{ pickupCodeInfo.orderNo }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">商家名称：</text>
          <text class="info-value">{{ pickupCodeInfo.merchantName }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">商家地址：</text>
          <text class="info-value">{{ pickupCodeInfo.merchantAddress }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">商家电话：</text>
          <text class="info-value">{{ pickupCodeInfo.merchantPhone }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">预计取餐时间：</text>
          <text class="info-value">{{ formatPickupTime(pickupCodeInfo.expectedPickupTime) }}</text>
        </view>
      </view>

      <!-- 使用提示 -->
      <view class="pickup-tips">
        <text class="tips-text">请到店出示此取餐码给商家，完成取餐</text>
      </view>
    </view>

    <view class="pickup-code-actions">
      <wd-button type="primary" size="large" @click="closePickupCodeModal">确定</wd-button>
    </view>
  </view>
</wd-popup>
```

### 3. JavaScript逻辑实现

#### 3.1 状态管理

```typescript
// 取餐码弹窗状态
const showPickupCodeModal = ref(false)
const pickupCodeLoading = ref(false)
const pickupCodeInfo = ref<PickupCodeInfo | null>(null)
```

#### 3.2 核心函数

```typescript
/**
 * 查看取餐码
 */
const viewPickupCode = async (orderId: number) => {
  try {
    pickupCodeLoading.value = true
    showPickupCodeModal.value = true
    pickupCodeInfo.value = null

    console.log(`🏪 获取订单${orderId}的取餐码`)
    const result = await getPickupCode(orderId)
    
    console.log('🎫 取餐码信息:', {
      orderId: result.orderID,
      orderNo: result.orderNo,
      pickupCode: result.pickupCode,
      pickupCodeUsed: result.pickupCodeUsed,
      merchantName: result.merchantName,
      expectedPickupTime: result.expectedPickupTime,
    })
    
    pickupCodeInfo.value = result
  } catch (error: any) {
    console.error('获取取餐码失败:', error)
    uni.showToast({
      title: error.message || '获取取餐码失败',
      icon: 'error',
    })
    showPickupCodeModal.value = false
  } finally {
    pickupCodeLoading.value = false
  }
}

/**
 * 关闭取餐码弹窗
 */
const closePickupCodeModal = () => {
  showPickupCodeModal.value = false
  pickupCodeInfo.value = null
}

/**
 * 格式化取餐时间
 */
const formatPickupTime = (time: string) => {
  const date = new Date(time)
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  
  return `${year}-${month}-${day} ${hours}:${minutes}`
}
```

## 🎨 UI设计特点

### 1. 取餐码显示区域
- **渐变背景**：使用橙色渐变背景突出显示
- **大字体**：取餐码使用48rpx大字体，易于识别
- **等宽字体**：使用Courier New字体，数字对齐美观
- **状态标识**：右上角显示使用状态（已使用/未使用）

### 2. 信息展示区域
- **清晰布局**：使用标签-值的形式展示信息
- **背景区分**：使用浅灰色背景区分信息区域
- **自适应文本**：支持长文本自动换行

### 3. 用户提示
- **醒目提示**：使用橙色背景提示用户使用方法
- **友好文案**：提供清晰的使用指导

## 🔄 功能流程

1. **条件判断**：系统检查订单是否为自取订单（`delivery_type = 2`）
2. **按钮显示**：符合条件的订单显示"查看取餐码"按钮
3. **用户点击**：用户点击按钮触发`viewPickupCode`函数
4. **API调用**：调用`getPickupCode`接口获取取餐码信息
5. **数据展示**：在弹窗中展示取餐码和相关信息
6. **用户操作**：用户查看取餐码信息，点击确定关闭弹窗

## 📊 API接口规范

### 请求
- **方法**：GET
- **路径**：`/v1/user/takeout/order/pickup-code/{orderID}`
- **参数**：orderID（路径参数）

### 响应
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "orderID": 123,
    "orderNo": "TO202501310001",
    "pickupCode": "123456",
    "pickupCodeUsed": false,
    "deliveryType": 2,
    "deliveryTypeText": "到店自取",
    "merchantName": "美味餐厅",
    "merchantAddress": "北京市朝阳区xxx街xxx号",
    "merchantPhone": "13800138000",
    "expectedPickupTime": "2025-01-31 18:00:00"
  }
}
```

## 🎯 关键特性

### 1. 条件显示
- 只有自取订单才显示"查看取餐码"按钮
- 配送订单继续显示"查看物流"按钮
- 保持原有功能不受影响

### 2. 错误处理
- API调用失败时显示错误提示
- 自动关闭弹窗避免用户困惑
- 详细的控制台日志便于调试

### 3. 用户体验
- 加载状态提示用户等待
- 清晰的取餐码显示
- 完整的商家和订单信息
- 友好的使用提示

### 4. 响应式设计
- 弹窗宽度自适应屏幕
- 文本自动换行处理
- 移动端友好的交互

## 📁 修改文件清单

### API相关
- ✅ `src/api/takeout.ts` - 添加getPickupCode接口
- ✅ `src/api/takeout.typings.ts` - 添加PickupCodeInfo类型定义

### 页面实现
- ✅ `src/pages/order/list.vue` - 添加取餐码功能和UI

### 测试文档
- ✅ `test-pickup-code-feature.js` - 自动化测试脚本
- ✅ `docs/pickup-code-feature.md` - 本文档

## 🚀 后续建议

1. **功能测试**：在开发环境中测试完整的取餐码流程
2. **API联调**：与后端确认接口实现和数据格式
3. **用户测试**：收集用户对取餐码功能的使用反馈
4. **性能优化**：考虑取餐码信息的缓存策略
5. **安全考虑**：确保取餐码信息的安全传输和显示

现在订单列表页面已经完整实现了取餐码查看功能，用户可以方便地查看自取订单的取餐码信息！
