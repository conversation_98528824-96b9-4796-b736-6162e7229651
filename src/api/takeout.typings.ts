/**
 * 外卖模块类型定义
 *
 * 该文件定义了外卖系统相关的数据类型，包括商家、外卖商品、购物车、订单等。
 * 与后端API接口保持一致，确保数据传输的准确性。
 */

// 商家信息
export interface Merchant {
  id: number
  name: string
  logo: string
  description: string
  category_id: number
  category_name: string
  contact_name: string
  contact_mobile: string
  address: string
  longitude?: number // 经度 GCJ02坐标系
  latitude?: number // 纬度 GCJ02坐标系
  delivery_fee: number
  min_order_amount: number
  delivery_time: number
  operation_status: number // 1:营业中,2:休息中
  rating: number
  month_sales: number
  food_count: number
  is_recommend: boolean
  distance?: number
  promotion_info: string
  business_hours: string
  opening_time: string
  closing_time: string
  photos?: string[]
  latest_announcement?: string
  support_pickup: number // 是否支持到店自取：0-不支持，1-支持
  created_at: string
  updated_at?: string
}

// 外卖分类类型
export interface ITakeoutCategory {
  id: number
  name: string
  icon?: string
  sort_order: number
  merchant_id: number
  is_active: boolean
  created_at: string
  updated_at: string
}

// 全局分类类型
export interface IGlobalCategory {
  id: number
  name: string
  icon?: string
  parent_id?: number
  level: number
  sort_order: number
  is_active: boolean
  children?: IGlobalCategory[]
  created_at: string
  updated_at: string
}

// 外卖商品类型
export interface ITakeoutFood {
  id: number
  name: string
  brief: string
  description?: string
  image: string
  price: number
  original_price: number
  packaging_fee: number
  preparation_time: number
  is_spicy: boolean
  is_combination: boolean
  has_variants: boolean
  sold_out: boolean
  min_price: number
  max_price: number
  daily_limit: number
  total_sold: number
  sales_count: number
  rating: number
  tags: string[]
  keywords?: string[]
  category_id: number
  category_name: string
  global_category_id?: number
  merchant_id?: number
  merchant_name?: string
  status: number
  audit_status: number
  audit_reason?: string
  audit_time?: string
  is_recommend: boolean
  variants: ITakeoutVariant[]
  combos?: TakeoutComboItem[]
  created_at: string
  updated_at?: string
}

// 商品规格类型
export interface TakeoutFoodVariant {
  id: number
  food_id: number
  name: string
  description: string
  image: string
  price: number
  original_price: number
  stock: number
  sold_count: number
  is_default: boolean
  sort_order: number
}

// 商品规格类型（与API返回格式一致）
export interface ITakeoutVariant {
  id: number
  food_id: number
  name: string
  description: string
  image: string
  price: number
  original_price: number
  stock: number
  sold_count: number
  is_default: boolean
  sort_order: number
}

// 套餐组件
export interface TakeoutComboItem {
  id: number
  combo_id: number
  name: string
  description: string
  min_selections: number
  max_selections: number
  is_required: boolean
  sort_order: number
  options: TakeoutComboOption[]
}

// 套餐选项
export interface TakeoutComboOption {
  id: number
  combo_item_id: number
  name: string
  description: string
  extra_price: number
  is_default: boolean
  sort_order: number
}

// 购物车项目
export interface TakeoutCartItem {
  cart_item_id: number
  merchant_id: number
  merchant_name: string
  food_id: number
  food_name: string
  food_image: string
  variant_id: number
  variant_name: string
  price: number
  original_price: number
  quantity: number
  packaging_fee: number
  subtotal: number
  combo_selections: ComboSelectionResponse[]
  remark: string
  selected: boolean
}

// 购物车套餐选择
export interface ComboSelectionResponse {
  combo_item_id: number
  combo_item_name: string
  combo_id: number
  combo_name: string
  selected_options: ComboOptionSelectionResponse[]
}

// 选中的套餐选项
export interface ComboOptionSelectionResponse {
  option_id: number
  option_name: string
  extra_price: number
  quantity: number
}

// 购物车
export interface TakeoutCartSummary {
  total_items: number
  total_quantity: number
  total_amount: number
  original_amount: number
  total_discount: number
  total_packaging_fee: number
  delivery_fee: number
  merchant_id: number
  merchant_name: string
  merchant_logo: string
  merchant_min_order_amount: number
  items: TakeoutCartItem[]
}

// 套餐选择类型
export interface ITakeoutComboSelection {
  combo_item_id: number
  option_ids: number[]
}

// 购物车项目类型
export interface ITakeoutCartItem {
  id: number
  user_id: number
  merchant_id: number
  food_id: number
  quantity: number
  price: number
  total_price: number
  variant_id?: number
  combo_selections?: ITakeoutComboSelection[]
  note?: string
  is_selected: boolean
  food: ITakeoutFood
  variant?: ITakeoutVariant
  created_at: string
  updated_at: string
}

// 购物车类型
export interface ITakeoutCart {
  items: ITakeoutCartItem[]
  total_count: number
  total_price: number
  delivery_fee: number
  final_price: number
  merchant_id?: number
  merchant?: IMerchant
}

// 订单状态枚举
export enum OrderStatus {
  PENDING_PAYMENT = 1, // 待付款
  PENDING_CONFIRM = 2, // 待确认
  PREPARING = 3, // 备餐中
  DELIVERING = 4, // 配送中
  COMPLETED = 5, // 已完成
  CANCELLED = 6, // 已取消
}

// 支付状态枚举
export enum PaymentStatus {
  UNPAID = 1, // 未支付
  PAID = 2, // 已支付
  REFUNDED = 3, // 已退款
}

// 订单
export interface TakeoutOrder {
  orderID: number
  orderNo: string
  userID: number
  merchantID: number
  totalAmount: number
  payAmount: number
  discountAmount: number
  orderStatus: number
  payStatus: number
  deliveryStatus: number
  deliveryFee: number
  deliveryInfo?: TakeoutDelivery
  items: TakeoutOrderItem[]
  paymentMethod: string
  remark: string
  couponInfo?: CouponInfo
  isRated: boolean
  createTime: string
  payTime?: string
  deliveryTime?: string
  completeTime?: string
  cancelTime?: string
  cancelReason?: string
  statusText: string
}

// 订单项目
export interface TakeoutOrderItem {
  id: number
  orderID: number
  productID: number
  productName: string
  productType: string
  price: number
  quantity: number
  amount: number
  specText: string
  image: string
  isCombination: boolean
  comboItems: string[]
}

// 配送信息
export interface TakeoutDelivery {
  deliveryStaffID: number
  deliveryStaffName: string
  deliveryStaffPhone: string
  deliveryStatus: number
  deliveryAddress: string
  deliveryDistance: number
  expectedTime: string
  startTime?: string
  endTime?: string
}

// 优惠券信息
export interface CouponInfo {
  couponID: number
  couponName: string
  couponType: number
  couponValue: number
}

// API请求参数类型

// 商家列表查询参数
export interface MerchantQueryRequest {
  keyword?: string
  category_id?: number
  distance_max?: number
  latitude?: number
  longitude?: number
  min_rating?: number
  has_promotion?: boolean
  is_recommend?: boolean
  sort_by?: string
  sort_order?: string
  page?: number
  page_size?: number
}

// 外卖商品列表查询参数
export interface TakeoutFoodListQuery {
  merchant_id: number
  category_id?: number
  keyword?: string
  sort_by?: string
  sort_order?: string
  page?: number
  page_size?: number
}

// 添加到购物车参数
export interface AddTakeoutToCartRequest {
  food_id: number
  variant_id?: number
  quantity: number
  remark?: string
  combo_selections?: CartComboSelectionRequest[]
}

// 购物车套餐选择请求
export interface CartComboSelectionRequest {
  combo_item_id: number
  selected_options: ComboOptionSelectionRequest[]
}

// 套餐选项选择请求
export interface ComboOptionSelectionRequest {
  option_id: number
  quantity: number
}

// 更新购物车参数
export interface UpdateTakeoutCartItemRequest {
  CartItemID: number
  quantity: number
  remark?: string
  combo_selections?: ComboSelectionRequest[]
}

// 套餐选择请求
export interface ComboSelectionRequest {
  combo_item_id: number
  selected_options: ComboOptionSelectionRequest[]
}

// 选择购物车项请求
export interface SelectCartItemRequest {
  cart_item_ids: number[]
  selected: boolean
}

// 创建订单参数
export interface CreateTakeoutOrderRequest {
  takeoutAddressID: number
  deliveryTime?: string
  remark?: string
  paymentMethod: string
  couponID?: number
  cartItemIDs: number[]
  deliveryType?: number // 配送方式：0-配送订单，2-自提订单
}

// 订单列表查询参数
export interface OrderListQuery {
  status?: number
  page?: number
  page_size?: number
}

// 取餐码信息
export interface PickupCodeInfo {
  orderID: number
  orderNo: string
  pickupCode: string
  pickupCodeUsed: boolean
  deliveryType: number
  deliveryTypeText: string
  merchantName: string
  merchantAddress: string
  merchantPhone: string
  expectedPickupTime: string
}

// API响应类型

// 分页响应
export interface PaginatedResponse<T> {
  total: number
  list: T[]
}

// 商家列表响应
export interface MerchantResponseList {
  total: number
  list: Merchant[]
}

// 外卖商品列表响应
export interface TakeoutFoodListResponse {
  total: number
  list: TakeoutFood[]
}

// 订单列表响应
export interface TakeoutOrderListResponse {
  total: number
  page: number
  pageSize: number
  totalPage: number
  list: TakeoutOrder[]
}

// 外卖分类列表响应
export interface TakeoutCategoryResponseList {
  total: number
  list: TakeoutCategory[]
}

// 外卖分类
export interface TakeoutCategory {
  id: number
  merchant_id: number
  name: string
  description: string
  image: string
  parent_id: number
  level: number
  sort_order: number
  is_visible: boolean
  created_at: string
  updated_at: string
  food_count: number
  children: TakeoutCategory[]
}

// 类型别名
export type ITakeoutMerchant = Merchant
export type IMerchant = Merchant
export type TakeoutFood = ITakeoutFood
